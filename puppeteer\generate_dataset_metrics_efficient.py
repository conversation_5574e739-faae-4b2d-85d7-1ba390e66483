#!/usr/bin/env python3
"""
Memory-efficient script to generate dataset_metrics.npz file for MoCapAct dataset.
Uses online statistics computation to avoid loading all data into memory.
"""

import os
import sys
import argparse
from glob import glob
from pathlib import Path
import h5py
import numpy as np
from tqdm import tqdm
import gc

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from envs.tracking import TRACKING_OBSERVABLES


class OnlineStats:
    """Online computation of mean and variance using <PERSON><PERSON><PERSON>'s algorithm"""
    def __init__(self):
        self.count = 0
        self.mean = None
        self.M2 = None
    
    def update(self, data):
        """Update statistics with new data batch"""
        if isinstance(data, list):
            data = np.array(data)
        
        if len(data.shape) == 1:
            data = data.reshape(1, -1)
        
        for sample in data:
            self.count += 1
            if self.mean is None:
                self.mean = np.zeros_like(sample, dtype=np.float64)
                self.M2 = np.zeros_like(sample, dtype=np.float64)
            
            delta = sample - self.mean
            self.mean += delta / self.count
            delta2 = sample - self.mean
            self.M2 += delta * delta2
    
    def get_stats(self):
        """Get final mean and variance"""
        if self.count < 2:
            return self.mean.astype(np.float32), np.zeros_like(self.mean, dtype=np.float32)
        variance = self.M2 / (self.count - 1)
        return self.mean.astype(np.float32), variance.astype(np.float32)


def compute_dataset_metrics_efficient(data_dir, output_path=None, max_files_per_batch=10):
    """
    Compute dataset statistics efficiently using online algorithms
    
    Args:
        data_dir: Directory containing HDF5 files
        output_path: Path to save the metrics file (default: data_dir/dataset_metrics.npz)
        max_files_per_batch: Maximum number of files to process at once
    """
    if output_path is None:
        output_path = os.path.join(data_dir, 'dataset_metrics.npz')
    
    print(f"Computing dataset metrics efficiently for: {data_dir}")
    print(f"Output will be saved to: {output_path}")
    
    # Find all HDF5 files
    hdf5_files = glob(os.path.join(data_dir, '*.hdf5'))
    if not hdf5_files:
        raise ValueError(f"No HDF5 files found in {data_dir}")
    
    print(f"Found {len(hdf5_files)} HDF5 files")
    
    # Initialize online statistics
    obs_stats = OnlineStats()
    act_stats = OnlineStats()
    mean_act_stats = OnlineStats()
    
    snippet_returns = {}
    advantages = {}
    values = {}
    
    # Process files in batches to manage memory
    for batch_start in range(0, len(hdf5_files), max_files_per_batch):
        batch_end = min(batch_start + max_files_per_batch, len(hdf5_files))
        batch_files = hdf5_files[batch_start:batch_end]
        
        print(f"Processing batch {batch_start//max_files_per_batch + 1}/{(len(hdf5_files)-1)//max_files_per_batch + 1}")
        
        for fname in tqdm(batch_files, desc="Processing files in batch"):
            try:
                with h5py.File(fname, 'r') as dset:
                    # Get all clip snippets
                    clip_snippets = [k for k in dset.keys() if k.startswith('CMU')]
                    
                    for snippet in clip_snippets:
                        try:
                            # Get number of rollouts
                            n_start_rollouts = dset['n_start_rollouts'][...]
                            n_rsi_rollouts = dset['n_rsi_rollouts'][...]
                            
                            # Process start rollouts
                            for i in range(min(n_start_rollouts, 50)):  # Limit to avoid memory issues
                                episode_key = f"{snippet}/{i}"
                                
                                # Get observations and actions
                                obs = dset[f"{snippet}/{i}/observations/proprioceptive"][...]
                                actions = dset[f"{snippet}/{i}/actions"][...]
                                mean_actions = dset[f"{snippet}/{i}/mean_actions"][...]
                                
                                # Update online statistics
                                obs_stats.update(obs)
                                act_stats.update(actions)
                                mean_act_stats.update(mean_actions)
                                
                                # Get values and advantages if available (store limited amount)
                                if f"{snippet}/{i}/values" in dset and len(values) < 1000:
                                    vals = dset[f"{snippet}/{i}/values"][...]
                                    values[episode_key] = vals
                                
                                if f"{snippet}/{i}/advantages" in dset and len(advantages) < 1000:
                                    advs = dset[f"{snippet}/{i}/advantages"][...]
                                    advantages[episode_key] = advs
                                
                                # Get episode return if available
                                if f"{snippet}/start_metrics/episode_returns" in dset and len(snippet_returns) < 1000:
                                    if i < len(dset[f"{snippet}/start_metrics/episode_returns"]):
                                        snippet_returns[episode_key] = dset[f"{snippet}/start_metrics/episode_returns"][i]
                            
                            # Process RSI rollouts (limited)
                            for i in range(min(n_rsi_rollouts, 20)):  # Even more limited for RSI
                                episode_key = f"{snippet}/{i + n_start_rollouts}"
                                
                                obs = dset[f"{snippet}/{i + n_start_rollouts}/observations/proprioceptive"][...]
                                actions = dset[f"{snippet}/{i + n_start_rollouts}/actions"][...]
                                mean_actions = dset[f"{snippet}/{i + n_start_rollouts}/mean_actions"][...]
                                
                                obs_stats.update(obs)
                                act_stats.update(actions)
                                mean_act_stats.update(mean_actions)
                                
                                if f"{snippet}/{i + n_start_rollouts}/values" in dset and len(values) < 1000:
                                    vals = dset[f"{snippet}/{i + n_start_rollouts}/values"][...]
                                    values[episode_key] = vals
                                
                                if f"{snippet}/{i + n_start_rollouts}/advantages" in dset and len(advantages) < 1000:
                                    advs = dset[f"{snippet}/{i + n_start_rollouts}/advantages"][...]
                                    advantages[episode_key] = advs
                                
                                if f"{snippet}/rsi_metrics/episode_returns" in dset and len(snippet_returns) < 1000:
                                    if i < len(dset[f"{snippet}/rsi_metrics/episode_returns"]):
                                        snippet_returns[episode_key] = dset[f"{snippet}/rsi_metrics/episode_returns"][i]
                                        
                        except Exception as e:
                            print(f"Warning: Error processing {snippet} in {fname}: {e}")
                            continue
                            
            except Exception as e:
                print(f"Warning: Error processing file {fname}: {e}")
                continue
        
        # Force garbage collection after each batch
        gc.collect()
    
    # Get final statistics
    obs_mean, obs_var = obs_stats.get_stats()
    act_mean, act_var = act_stats.get_stats()
    mean_act_mean, mean_act_var = mean_act_stats.get_stats()
    
    print(f"Computed statistics:")
    print(f"  Observation count: {obs_stats.count}")
    print(f"  Action count: {act_stats.count}")
    print(f"  Observation shape: {obs_mean.shape}")
    print(f"  Action shape: {act_mean.shape}")
    print(f"  Number of episodes with values: {len(values)}")
    print(f"  Number of episodes with advantages: {len(advantages)}")
    print(f"  Number of episodes with returns: {len(snippet_returns)}")
    
    # Save metrics
    np.savez(
        output_path,
        count=obs_stats.count,
        proprio_mean=obs_mean,
        proprio_var=obs_var,
        act_mean=act_mean,
        act_var=act_var,
        mean_act_mean=mean_act_mean,
        mean_act_var=mean_act_var,
        snippet_returns=snippet_returns,
        advantages=advantages,
        values=values
    )
    
    print(f"Dataset metrics saved to: {output_path}")
    return output_path


def main():
    parser = argparse.ArgumentParser(description='Generate dataset_metrics.npz for MoCapAct dataset (memory efficient)')
    parser.add_argument('data_dir', help='Directory containing HDF5 files')
    parser.add_argument('--output', '-o', help='Output path for metrics file')
    parser.add_argument('--batch-size', '-b', type=int, default=5, help='Number of files to process per batch')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.data_dir):
        raise ValueError(f"Data directory does not exist: {args.data_dir}")
    
    compute_dataset_metrics_efficient(args.data_dir, args.output, args.batch_size)


if __name__ == '__main__':
    main()
