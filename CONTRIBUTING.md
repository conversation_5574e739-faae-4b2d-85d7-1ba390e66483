# Contributing to Puppeteer
We want to make contributing to this repository as easy and transparent as
possible.

## Pull requests
We actively welcome your pull requests.

1. Fork the repo and create your branch from `main`.
2. If you have added code that should be tested, add tests.
3. If you have changed APIs, update the documentation.
4. Make sure your code lints.
5. Issue that pull request!

## Issues
We use GitHub issues to track public bugs. Please ensure your description is
clear and has sufficient instructions to be able to reproduce the issue.

## License
By contributing to Puppeteer, you agree that your contributions will be licensed
under the `LICENSE` file in the root directory of this source tree.
