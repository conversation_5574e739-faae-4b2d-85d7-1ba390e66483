#!/usr/bin/env python3
"""
Script to generate dataset_metrics.npz file for MoCapAct dataset.
This file contains precomputed statistics needed for training.
"""

import os
import sys
import argparse
from glob import glob
from pathlib import Path
import h5py
import numpy as np
from tqdm import tqdm
import itertools

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from envs.tracking import TRACKING_OBSERVABLES


def compute_dataset_metrics(data_dir, output_path=None):
    """
    Compute dataset statistics and save to dataset_metrics.npz
    
    Args:
        data_dir: Directory containing HDF5 files
        output_path: Path to save the metrics file (default: data_dir/dataset_metrics.npz)
    """
    if output_path is None:
        output_path = os.path.join(data_dir, 'dataset_metrics.npz')
    
    print(f"Computing dataset metrics for: {data_dir}")
    print(f"Output will be saved to: {output_path}")
    
    # Find all HDF5 files
    hdf5_files = glob(os.path.join(data_dir, '*.hdf5'))
    if not hdf5_files:
        raise ValueError(f"No HDF5 files found in {data_dir}")
    
    print(f"Found {len(hdf5_files)} HDF5 files")
    
    # Initialize accumulators for online statistics computation
    obs_count = 0
    obs_sum = None
    obs_sum_sq = None

    act_count = 0
    act_sum = None
    act_sum_sq = None

    mean_act_count = 0
    mean_act_sum = None
    mean_act_sum_sq = None

    snippet_returns = {}
    advantages = {}
    values = {}
    
    # Get observable indices from first file
    with h5py.File(hdf5_files[0], 'r') as dset:
        obs_ind_dset = dset['observable_indices/walker']
        observable_indices = {
            f"walker/{k}": obs_ind_dset[k][...] for k in obs_ind_dset
        }
    
    # Process each file
    for fname in tqdm(hdf5_files, desc="Processing files"):
        with h5py.File(fname, 'r') as dset:
            # Get all clip snippets
            clip_snippets = [k for k in dset.keys() if k.startswith('CMU')]
            
            for snippet in clip_snippets:
                try:
                    # Get number of rollouts
                    n_start_rollouts = dset['n_start_rollouts'][...]
                    n_rsi_rollouts = dset['n_rsi_rollouts'][...]
                    
                    # Process start rollouts
                    for i in range(n_start_rollouts):
                        episode_key = f"{snippet}/{i}"
                        
                        # Get observations and actions
                        obs = dset[f"{snippet}/{i}/observations/proprioceptive"][...]
                        actions = dset[f"{snippet}/{i}/actions"][...]
                        mean_actions = dset[f"{snippet}/{i}/mean_actions"][...]
                        
                        # Get values and advantages if available
                        if f"{snippet}/{i}/values" in dset:
                            vals = dset[f"{snippet}/{i}/values"][...]
                            values[episode_key] = vals
                            all_values.extend(vals)
                        
                        if f"{snippet}/{i}/advantages" in dset:
                            advs = dset[f"{snippet}/{i}/advantages"][...]
                            advantages[episode_key] = advs
                            all_advantages.extend(advs)
                        
                        # Get episode return if available
                        if f"{snippet}/start_metrics/episode_returns" in dset:
                            if i < len(dset[f"{snippet}/start_metrics/episode_returns"]):
                                snippet_returns[episode_key] = dset[f"{snippet}/start_metrics/episode_returns"][i]
                        
                        all_observations.extend(obs)
                        all_actions.extend(actions)
                        all_mean_actions.extend(mean_actions)
                    
                    # Process RSI rollouts
                    for i in range(n_rsi_rollouts):
                        episode_key = f"{snippet}/{i + n_start_rollouts}"
                        
                        obs = dset[f"{snippet}/{i + n_start_rollouts}/observations/proprioceptive"][...]
                        actions = dset[f"{snippet}/{i + n_start_rollouts}/actions"][...]
                        mean_actions = dset[f"{snippet}/{i + n_start_rollouts}/mean_actions"][...]
                        
                        if f"{snippet}/{i + n_start_rollouts}/values" in dset:
                            vals = dset[f"{snippet}/{i + n_start_rollouts}/values"][...]
                            values[episode_key] = vals
                            all_values.extend(vals)
                        
                        if f"{snippet}/{i + n_start_rollouts}/advantages" in dset:
                            advs = dset[f"{snippet}/{i + n_start_rollouts}/advantages"][...]
                            advantages[episode_key] = advs
                            all_advantages.extend(advs)
                        
                        if f"{snippet}/rsi_metrics/episode_returns" in dset:
                            if i < len(dset[f"{snippet}/rsi_metrics/episode_returns"]):
                                snippet_returns[episode_key] = dset[f"{snippet}/rsi_metrics/episode_returns"][i]
                        
                        all_observations.extend(obs)
                        all_actions.extend(actions)
                        all_mean_actions.extend(mean_actions)
                        
                except Exception as e:
                    print(f"Warning: Error processing {snippet} in {fname}: {e}")
                    continue
    
    # Convert to numpy arrays
    all_observations = np.array(all_observations, dtype=np.float32)
    all_actions = np.array(all_actions, dtype=np.float32)
    all_mean_actions = np.array(all_mean_actions, dtype=np.float32)
    
    print(f"Collected {len(all_observations)} observations")
    print(f"Collected {len(all_actions)} actions")
    
    # Compute statistics
    count = len(all_observations)
    proprio_mean = np.mean(all_observations, axis=0)
    proprio_var = np.var(all_observations, axis=0)
    act_mean = np.mean(all_actions, axis=0)
    act_var = np.var(all_actions, axis=0)
    mean_act_mean = np.mean(all_mean_actions, axis=0)
    mean_act_var = np.var(all_mean_actions, axis=0)
    
    # Handle empty values/advantages
    if not values:
        values = {}
    if not advantages:
        advantages = {}
    if not snippet_returns:
        snippet_returns = {}
    
    print(f"Computed statistics:")
    print(f"  Count: {count}")
    print(f"  Observation shape: {proprio_mean.shape}")
    print(f"  Action shape: {act_mean.shape}")
    print(f"  Number of episodes with values: {len(values)}")
    print(f"  Number of episodes with advantages: {len(advantages)}")
    print(f"  Number of episodes with returns: {len(snippet_returns)}")
    
    # Save metrics
    np.savez(
        output_path,
        count=count,
        proprio_mean=proprio_mean,
        proprio_var=proprio_var,
        act_mean=act_mean,
        act_var=act_var,
        mean_act_mean=mean_act_mean,
        mean_act_var=mean_act_var,
        snippet_returns=snippet_returns,
        advantages=advantages,
        values=values
    )
    
    print(f"Dataset metrics saved to: {output_path}")
    return output_path


def main():
    parser = argparse.ArgumentParser(description='Generate dataset_metrics.npz for MoCapAct dataset')
    parser.add_argument('data_dir', help='Directory containing HDF5 files')
    parser.add_argument('--output', '-o', help='Output path for metrics file')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.data_dir):
        raise ValueError(f"Data directory does not exist: {args.data_dir}")
    
    compute_dataset_metrics(args.data_dir, args.output)


if __name__ == '__main__':
    main()
